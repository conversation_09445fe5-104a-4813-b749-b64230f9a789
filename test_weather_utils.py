#!/usr/bin/env python3
"""
Тест для модуля weather_utils.py
Проверяет корректность работы функций форматирования погодных данных
"""

import sys
import os

# Добавляем текущую директорию в путь для импорта
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from weather_utils import (
    create_weather_caption_current,
    create_weather_caption_today,
    create_weather_caption_3days,
    get_day_name,
    round_temperature,
    format_wind_speed,
    check_image_exists,
    get_image_path
)


def test_current_weather():
    """Тест функции create_weather_caption_current"""
    print("=== Тест текущей погоды ===")
    
    # Тестовые данные
    test_data = {
        'temp': 15,
        'feels_like': 13,
        'description': 'Переменная облачность',
        'wind_speed': 3.2,
        'wind_dir': 'SW',
        'humidity': 65,
        'pressure': 1013,
        'is_day': True
    }
    
    result = create_weather_caption_current(test_data)
    print(result)
    print()


def test_today_weather():
    """Тест функции create_weather_caption_today"""
    print("=== Тест прогноза на сегодня ===")
    
    # Тестовые данные
    test_data = {
        'periods': {
            'morning': {
                'name': 'Утром',
                'temp': 12,
                'feels_like': 10,
                'description': 'Ясно',
                'wind_speed': 2.1,
                'is_day': True
            },
            'day': {
                'name': 'Днём',
                'temp': 18,
                'feels_like': 18,
                'description': 'Солнечно',
                'wind_speed': 3.5,
                'is_day': True
            },
            'evening': {
                'name': 'Вечером',
                'temp': 14,
                'feels_like': 12,
                'description': 'Облачно',
                'wind_speed': 2.8,
                'is_day': False
            },
            'night': {
                'name': 'Ночью',
                'temp': 8,
                'feels_like': 6,
                'description': 'Ясно',
                'wind_speed': 1.5,
                'is_day': False
            }
        },
        'sunrise_display': 'Восход: 06:15',
        'sunset_formatted': '20:45'
    }
    
    result = create_weather_caption_today(test_data)
    print(result)
    print()


def test_3days_weather():
    """Тест функции create_weather_caption_3days"""
    print("=== Тест прогноза на 3 дня ===")
    
    # Тестовые данные
    test_data = [
        {
            'date': 'Пн, 31 июля',
            'temp_max': 22,
            'temp_min': 12,
            'description': 'Солнечно',
            'wind_speed': 3.0,
            'chance_of_rain': 10
        },
        {
            'date': 'Вт, 1 августа',
            'temp_max': 25,
            'temp_min': 15,
            'description': 'Переменная облачность',
            'wind_speed': 4.2,
            'chance_of_rain': 30
        },
        {
            'date': 'Ср, 2 августа',
            'temp_max': 19,
            'temp_min': 11,
            'description': 'Дождь',
            'wind_speed': 5.1,
            'chance_of_rain': 80
        }
    ]
    
    result = create_weather_caption_3days(test_data)
    print(result)
    print()


def test_utility_functions():
    """Тест вспомогательных функций"""
    print("=== Тест вспомогательных функций ===")
    
    # Тест get_day_name
    from datetime import datetime
    test_date = datetime(2025, 7, 31)  # Четверг
    day_name = get_day_name(test_date)
    print(f"День недели для {test_date.strftime('%Y-%m-%d')}: {day_name}")
    
    # Тест round_temperature
    temp = round_temperature(15.7)
    print(f"Округленная температура 15.7°C: {temp}°C")
    
    # Тест format_wind_speed
    wind = format_wind_speed(3.2)
    print(f"Скорость ветра 3.2: {wind}")
    
    # Тест get_image_path
    image_path = get_image_path('sunny.jpg')
    print(f"Путь к изображению: {image_path}")
    
    # Тест check_image_exists
    exists = check_image_exists('images/clear_day.png')
    print(f"Файл images/clear_day.png существует: {exists}")
    
    print()


def test_error_handling():
    """Тест обработки ошибок"""
    print("=== Тест обработки ошибок ===")
    
    # Тест с пустыми данными
    result1 = create_weather_caption_current({})
    print("Пустые данные текущей погоды:")
    print(result1)
    print()
    
    # Тест с None
    result2 = create_weather_caption_today(None)
    print("None данные прогноза на сегодня:")
    print(result2)
    print()
    
    # Тест с пустым списком
    result3 = create_weather_caption_3days([])
    print("Пустой список прогноза на 3 дня:")
    print(result3)
    print()


if __name__ == "__main__":
    print("Тестирование модуля weather_utils.py")
    print("=" * 50)
    
    try:
        test_current_weather()
        test_today_weather()
        test_3days_weather()
        test_utility_functions()
        test_error_handling()
        
        print("✅ Все тесты завершены успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()
