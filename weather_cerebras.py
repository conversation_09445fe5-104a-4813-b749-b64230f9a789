"""
Модуль для взаимодействия с Cerebras API.

Содержит функции для выбора подходящих изображений:
- select_image() - отправляет запрос с описанием погоды и получает
  название картинки от модели Qwen-3-235B-A22B-Instruct-2507

Этап 4 плана разработки - реализация обращения к Cerebras API.

Использование:
    from weather_cerebras import select_image
    from weather_config import AVAILABLE_IMAGE_NAMES

    # Асинхронный вызов
    image_name = await select_image(
        description="солнечно, температура 25°C, ветер 2 м/с",
        available_images=AVAILABLE_IMAGE_NAMES,
        current_time="30.07.2025 14:00 (MSK)"
    )

    # image_name будет содержать название картинки, например "sunny_day"

Особенности:
- Функция всегда возвращает валидное название изображения
- При ошибках API возвращается изображение по умолчанию
- Поддерживается автоматический fallback на похожие названия
- Все ошибки логируются, но не прерывают выполнение
"""

import asyncio
import json
import logging
from typing import List, Optional

import aiohttp

from weather_config import (
    CEREBRAS_API_URL,
    CEREBRAS_API_KEY,
    CEREBRAS_MODEL,
    AVAILABLE_IMAGE_NAMES
)

# Настройка логирования
logger = logging.getLogger(__name__)


async def select_image(
    description: str,
    available_images: List[str],
    current_time: str
) -> str:
    """
    Выбирает подходящее изображение для погодных условий с помощью Cerebras API.

    Отправляет запрос к модели Qwen-3-235B-A22B-Instruct-2507 с описанием
    текущей погоды и списком доступных изображений. Модель возвращает
    название наиболее подходящей картинки.

    Args:
        description: Краткое описание погодных условий
                    (например, "пасмурно, температура 15 °C, ветер 3 м/с")
        available_images: Список названий доступных изображений
        current_time: Текущее время в формате "dd.MM.yyyy HH:MM (MSK)"

    Returns:
        str: Название выбранной картинки из списка available_images
             или картинка по умолчанию при ошибке

    Raises:
        Функция не выбрасывает исключения, всегда возвращает валидное название
    """
    # Формируем текст запроса для модели
    prompt = _create_prompt(description, available_images, current_time)

    # Подготавливаем JSON для запроса к Cerebras API
    request_data = {
        "model": CEREBRAS_MODEL,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0,  # Детерминированный выбор
        "top_p": 1,
        "max_tokens": 50,  # Достаточно для названия картинки
        "stream": False
    }

    # Заголовки для запроса
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {CEREBRAS_API_KEY}"
    }

    try:
        # Отправляем асинхронный POST-запрос
        async with aiohttp.ClientSession() as session:
            async with session.post(
                CEREBRAS_API_URL,
                json=request_data,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:

                # Проверяем статус ответа
                if response.status != 200:
                    logger.error(
                        f"Cerebras API вернул статус {response.status}: {await response.text()}"
                    )
                    return _get_default_image(available_images)

                # Парсим JSON ответ
                response_data = await response.json()

                # Извлекаем название картинки из ответа
                selected_image = _extract_image_name(response_data, available_images)

                logger.info(f"Cerebras выбрал изображение: {selected_image}")
                return selected_image

    except asyncio.TimeoutError:
        logger.error("Таймаут при запросе к Cerebras API")
        return _get_default_image(available_images)

    except aiohttp.ClientError as e:
        logger.error(f"Ошибка HTTP-клиента при запросе к Cerebras: {e}")
        return _get_default_image(available_images)

    except json.JSONDecodeError as e:
        logger.error(f"Ошибка парсинга JSON ответа от Cerebras: {e}")
        return _get_default_image(available_images)

    except Exception as e:
        logger.error(f"Неожиданная ошибка при запросе к Cerebras: {e}")
        return _get_default_image(available_images)


def _create_prompt(description: str, available_images: List[str], current_time: str) -> str:
    """
    Создает текст запроса для модели Cerebras.

    Args:
        description: Описание погодных условий
        available_images: Список доступных изображений
        current_time: Текущее время

    Returns:
        str: Сформированный промпт для модели
    """
    images_list = ", ".join(available_images)

    prompt = (
        f"Сейчас {current_time}. "
        f"Погодные условия: {description}. "
        f"Выбери лучшую картинку из списка: {images_list}. "
        f"Ответь только названием файла без дополнительных слов."
    )

    return prompt


def _extract_image_name(response_data: dict, available_images: List[str]) -> str:
    """
    Извлекает название картинки из ответа Cerebras API.

    Args:
        response_data: JSON ответ от Cerebras API
        available_images: Список доступных изображений для валидации

    Returns:
        str: Название картинки или картинка по умолчанию
    """
    try:
        # Извлекаем текст ответа модели
        content = response_data["choices"][0]["message"]["content"].strip()

        # Проверяем, что выбранная картинка есть в списке доступных
        if content in available_images:
            return content

        # Если модель вернула неизвестное название, ищем похожее
        content_lower = content.lower()
        for image_name in available_images:
            if content_lower in image_name.lower() or image_name.lower() in content_lower:
                logger.warning(
                    f"Cerebras вернул '{content}', используем похожее: {image_name}"
                )
                return image_name

        # Если ничего не найдено, используем картинку по умолчанию
        logger.warning(
            f"Cerebras вернул неизвестное изображение '{content}', "
            f"используем изображение по умолчанию"
        )
        return _get_default_image(available_images)

    except (KeyError, IndexError, TypeError) as e:
        logger.error(f"Ошибка извлечения названия картинки из ответа: {e}")
        return _get_default_image(available_images)


def _get_default_image(available_images: List[str]) -> str:
    """
    Возвращает изображение по умолчанию.

    Args:
        available_images: Список доступных изображений

    Returns:
        str: Название изображения по умолчанию
    """
    # Приоритет: облачно днем -> любое облачное -> первое в списке
    default_options = ['cloudy_day', 'overcast_day', 'partly_cloudy_day']

    for default in default_options:
        if default in available_images:
            return default

    # Если ничего не найдено, возвращаем первое доступное
    return available_images[0] if available_images else 'cloudy_day'


# =============================================================================
# ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ДЛЯ ТЕСТИРОВАНИЯ
# =============================================================================

async def test_select_image():
    """
    Тестовая функция для проверки работы select_image.
    """
    test_description = "пасмурно, температура 15 °C, ветер 3 м/с"
    test_time = "30.07.2025 12:00 (MSK)"
    test_images = AVAILABLE_IMAGE_NAMES[:10]  # Первые 10 изображений для теста

    print(f"Тестируем select_image...")
    print(f"Описание: {test_description}")
    print(f"Время: {test_time}")
    print(f"Доступные изображения: {test_images}")

    result = await select_image(test_description, test_images, test_time)
    print(f"Результат: {result}")

    return result


async def test_error_handling():
    """
    Тестирует обработку ошибок при недоступности API.
    """
    print(f"\nТестируем обработку ошибок...")

    # Временно заменяем API ключ на неправильный
    global CEREBRAS_API_KEY
    original_key = CEREBRAS_API_KEY

    # Импортируем модуль заново с неправильным ключом
    import weather_config
    weather_config.CEREBRAS_API_KEY = "invalid_key"

    test_description = "солнечно, температура 25 °C"
    test_time = "30.07.2025 14:00 (MSK)"
    test_images = ['sunny_day', 'cloudy_day', 'rainy_day']

    result = await select_image(test_description, test_images, test_time)
    print(f"Результат при ошибке API: {result}")
    print(f"Ожидаемый fallback: {_get_default_image(test_images)}")

    # Восстанавливаем правильный ключ
    weather_config.CEREBRAS_API_KEY = original_key

    return result


if __name__ == "__main__":
    # Запуск тестирования при прямом вызове модуля
    async def run_tests():
        await test_select_image()
        await test_error_handling()

    asyncio.run(run_tests())
