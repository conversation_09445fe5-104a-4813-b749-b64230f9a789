#!/usr/bin/env python3
"""
Тестирование модуля weather_handlers.py

Проверяет:
- Импорт всех функций
- Инициализацию хранилища
- Базовую функциональность обработчиков
"""

import asyncio
import logging
from unittest.mock import Mock, AsyncMock, patch

# Настройка логирования для тестов
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_imports():
    """Тестирует импорт всех функций из weather_handlers"""
    try:
        from weather_handlers import (
            on_channel_post, send_initial_messages,
            update_3days, update_today, update_current,
            get_storage, has_active_messages, clear_all_messages, get_message_ids
        )
        
        print("✅ Все функции успешно импортированы:")
        print(f"  - on_channel_post: {on_channel_post}")
        print(f"  - send_initial_messages: {send_initial_messages}")
        print(f"  - update_3days: {update_3days}")
        print(f"  - update_today: {update_today}")
        print(f"  - update_current: {update_current}")
        print(f"  - get_storage: {get_storage}")
        print(f"  - has_active_messages: {has_active_messages}")
        print(f"  - clear_all_messages: {clear_all_messages}")
        print(f"  - get_message_ids: {get_message_ids}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Ошибка импорта: {e}")
        return False


def test_storage_functions():
    """Тестирует функции работы с хранилищем"""
    try:
        from weather_handlers import get_storage, has_active_messages, get_message_ids
        
        # Получаем хранилище
        storage = get_storage()
        print(f"✅ Хранилище получено: {type(storage)}")
        
        # Проверяем наличие активных сообщений
        has_messages = has_active_messages()
        print(f"✅ Проверка активных сообщений: {has_messages}")
        
        # Получаем message_ids
        message_ids = get_message_ids()
        print(f"✅ Message IDs: {message_ids}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования хранилища: {e}")
        return False


async def test_channel_post_handler():
    """Тестирует обработчик постов канала с мокированными данными"""
    try:
        from weather_handlers import on_channel_post
        
        # Создаем мок объекты
        mock_update = Mock()
        mock_context = Mock()
        
        # Настраиваем мок для поста без ключевого слова
        mock_update.channel_post = Mock()
        mock_update.channel_post.text = "Обычный пост без ключевого слова"
        mock_update.channel_post.caption = None
        mock_update.channel_post.chat_id = 12345
        
        # Вызываем обработчик (не должен ничего делать)
        await on_channel_post(mock_update, mock_context)
        print("✅ Обработчик корректно игнорирует посты без ключевого слова")
        
        # Настраиваем мок для поста с ключевым словом
        mock_update.channel_post.text = "Проверим weather сегодня"
        
        # Мокируем send_initial_messages чтобы избежать реальных API вызовов
        with patch('weather_handlers.send_initial_messages') as mock_send:
            mock_send.return_value = None
            await on_channel_post(mock_update, mock_context)
            print("✅ Обработчик корректно обрабатывает посты с ключевым словом")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования обработчика: {e}")
        return False


async def test_update_functions():
    """Тестирует функции обновления сообщений"""
    try:
        from weather_handlers import update_3days, update_today, update_current
        
        # Создаем мок контекст
        mock_context = Mock()
        
        # Мокируем _update_weather_message чтобы избежать реальных API вызовов
        with patch('weather_handlers._update_weather_message') as mock_update:
            mock_update.return_value = None
            
            # Тестируем каждую функцию обновления
            await update_3days(mock_context)
            print("✅ update_3days выполнена без ошибок")
            
            await update_today(mock_context)
            print("✅ update_today выполнена без ошибок")
            
            await update_current(mock_context)
            print("✅ update_current выполнена без ошибок")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования функций обновления: {e}")
        return False


async def main():
    """Основная функция тестирования"""
    print("🧪 Начинаем тестирование weather_handlers.py")
    print("=" * 50)
    
    tests = [
        ("Импорт функций", test_imports()),
        ("Функции хранилища", test_storage_functions()),
        ("Обработчик постов канала", test_channel_post_handler()),
        ("Функции обновления", test_update_functions())
    ]
    
    results = []
    for test_name, test_coro in tests:
        print(f"\n📋 Тест: {test_name}")
        print("-" * 30)
        
        if asyncio.iscoroutine(test_coro):
            result = await test_coro
        else:
            result = test_coro
            
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ ПРОЙДЕН" if result else "❌ ПРОВАЛЕН"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nИтого: {passed}/{len(results)} тестов пройдено")
    
    if passed == len(results):
        print("🎉 Все тесты пройдены успешно!")
        print("✅ Этап 8 (weather_handlers.py) реализован корректно")
    else:
        print("⚠️ Некоторые тесты провалены, требуется доработка")


if __name__ == "__main__":
    asyncio.run(main())
