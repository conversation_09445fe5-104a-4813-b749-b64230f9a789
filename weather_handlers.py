"""
Обработчики обновлений Telegram для бота погоды.

Содержит:
- on_channel_post() - обработчик постов в канале с ключевым словом "weather"
- send_initial_messages() - отправка первого набора сообщений
- update_week(), update_today(), update_current() - функции обновления сообщений
- Сохранение message_id в хранилище
"""

import logging
import asyncio
import aiohttp
from datetime import datetime
from typing import Optional, Dict, Any, List

from telegram import Update, InputMediaPhoto
from telegram.ext import ContextTypes
from telegram.error import TelegramError

# Импорт модулей проекта
from weather_config import (
    TRIGGER_KEYWORD, CHANNEL_ID, AVAILABLE_IMAGE_NAMES,
    get_image_path, TZ
)
from weather_api import (
    fetch_current_weather, fetch_forecast_weather, fetch_astronomy_data,
    parse_current_weatherapi, parse_forecast_weatherapi, parse_today_weatherapi
)
from weather_cerebras import select_image
from weather_utils import (
    create_weather_caption_current, create_weather_caption_today,
    create_weather_caption_3days, truncate_text
)
from weather_storage import Storage

# Настройка логирования
logger = logging.getLogger(__name__)

# Глобальное хранилище
storage = Storage()


async def on_channel_post(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Обработчик постов в канале. Проверяет наличие ключевого слова 'weather'
    и инициирует создание прогнозов погоды.

    Args:
        update: Объект обновления Telegram
        context: Контекст выполнения
    """
    try:
        # Проверяем, что это пост в канале
        if not update.channel_post:
            return

        # Получаем текст поста
        post_text = update.channel_post.text or update.channel_post.caption or ""

        # Проверяем наличие ключевого слова (регистр не важен)
        if TRIGGER_KEYWORD.lower() not in post_text.lower():
            return

        # Получаем ID канала
        chat_id = update.channel_post.chat_id

        # Сохраняем ID канала в хранилище
        storage.set_channel_id(chat_id)

        # Обновляем время последнего запроса
        storage.update_weather_request_time()

        logger.info(f"Обнаружено ключевое слово '{TRIGGER_KEYWORD}' в канале {chat_id}")

        # Отправляем начальные сообщения
        await send_initial_messages(chat_id, context)

    except Exception as e:
        logger.error(f"Ошибка в обработчике канала: {e}")


async def send_initial_messages(chat_id: int, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Отправляет первый набор сообщений с прогнозами погоды.

    Порядок отправки (согласно плану):
    1. Погода на 3 дня
    2. Погода на сегодня
    3. Погода сейчас

    Args:
        chat_id: ID канала для отправки сообщений
        context: Контекст выполнения
    """
    try:
        logger.info(f"Начинаем отправку начальных сообщений в канал {chat_id}")

        # Создаем HTTP сессию для запросов к API
        async with aiohttp.ClientSession() as session:

            # Получаем данные о погоде
            current_data = await fetch_current_weather(session)
            forecast_data = await fetch_forecast_weather(session, days=3)

            if not current_data or not forecast_data:
                logger.error("Не удалось получить данные о погоде")
                return

            # Парсим данные
            current_parsed = parse_current_weatherapi(current_data)
            forecast_parsed = parse_forecast_weatherapi(forecast_data)
            today_parsed = parse_today_weatherapi(forecast_data)

            if not all([current_parsed, forecast_parsed, today_parsed]):
                logger.error("Ошибка парсинга данных о погоде")
                return

            # Получаем текущее время для Cerebras
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M (MSK)")

            # 1. Отправляем прогноз на 3 дня
            await _send_weather_message(
                context, chat_id, "3days",
                forecast_parsed, current_time, session
            )

            # 2. Отправляем прогноз на сегодня
            await _send_weather_message(
                context, chat_id, "today",
                today_parsed, current_time, session
            )

            # 3. Отправляем текущую погоду
            await _send_weather_message(
                context, chat_id, "current",
                current_parsed, current_time, session
            )

        logger.info("Все начальные сообщения отправлены успешно")

    except Exception as e:
        logger.error(f"Ошибка отправки начальных сообщений: {e}")


async def _send_weather_message(
    context: ContextTypes.DEFAULT_TYPE,
    chat_id: int,
    message_type: str,
    weather_data: Any,
    current_time: str,
    session: aiohttp.ClientSession
) -> None:
    """
    Отправляет одно сообщение с погодой определенного типа.

    Args:
        context: Контекст выполнения
        chat_id: ID канала
        message_type: Тип сообщения ('3days', 'today', 'current')
        weather_data: Данные о погоде
        current_time: Текущее время для Cerebras
        session: HTTP сессия
    """
    try:
        # Формируем подпись в зависимости от типа
        if message_type == "3days":
            caption = create_weather_caption_3days(weather_data)
            description = f"прогноз на 3 дня"
        elif message_type == "today":
            caption = create_weather_caption_today(weather_data)
            description = f"прогноз на сегодня"
        elif message_type == "current":
            caption = create_weather_caption_current(weather_data)
            description = f"текущая погода: {weather_data.get('description', '')}, {weather_data.get('temp', 0)}°C"
        else:
            logger.error(f"Неизвестный тип сообщения: {message_type}")
            return

        # Обрезаем подпись до лимита Telegram
        caption = truncate_text(caption, 1024)

        # Выбираем изображение через Cerebras
        selected_image = await select_image(description, AVAILABLE_IMAGE_NAMES, current_time)
        image_path = get_image_path(selected_image)

        logger.info(f"Отправляем {message_type}: изображение {selected_image}")

        # Отправляем сообщение с фото
        with open(image_path, 'rb') as photo:
            message = await context.bot.send_photo(
                chat_id=chat_id,
                photo=photo,
                caption=caption,
                parse_mode='HTML'
            )

        # Сохраняем message_id в хранилище
        storage.update_message_id(message_type, message.message_id)

        # Сохраняем file_id изображения для будущих обновлений
        if message.photo:
            file_id = message.photo[-1].file_id  # Берем самое большое фото
            storage.save_file_id(selected_image, file_id)

        logger.info(f"Сообщение {message_type} отправлено, message_id: {message.message_id}")

    except Exception as e:
        logger.error(f"Ошибка отправки сообщения {message_type}: {e}")


# Функции обновления сообщений для планировщика

async def update_3days(context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Обновляет сообщение с прогнозом на 3 дня.
    Вызывается планировщиком каждые 12 часов.

    Args:
        context: Контекст выполнения с данными задачи
    """
    await _update_weather_message(context, "3days")


async def update_today(context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Обновляет сообщение с прогнозом на сегодня.
    Вызывается планировщиком каждый час.

    Args:
        context: Контекст выполнения с данными задачи
    """
    await _update_weather_message(context, "today")


async def update_current(context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Обновляет сообщение с текущей погодой.
    Вызывается планировщиком каждые 10 минут.

    Args:
        context: Контекст выполнения с данными задачи
    """
    await _update_weather_message(context, "current")


async def _update_weather_message(context: ContextTypes.DEFAULT_TYPE, message_type: str) -> None:
    """
    Универсальная функция обновления сообщения с погодой.

    Args:
        context: Контекст выполнения
        message_type: Тип сообщения ('3days', 'today', 'current')
    """
    try:
        # Получаем message_id из хранилища
        message_id = storage.get_message_id(message_type)
        if not message_id:
            logger.warning(f"Нет message_id для типа {message_type}, пропускаем обновление")
            return

        # Получаем channel_id
        channel_id = storage.get_channel_id()
        if not channel_id:
            logger.error("Нет channel_id в хранилище")
            return

        logger.info(f"Обновляем сообщение {message_type} (message_id: {message_id})")

        # Создаем HTTP сессию для запросов к API
        async with aiohttp.ClientSession() as session:

            # Получаем свежие данные о погоде
            if message_type == "current":
                weather_data_raw = await fetch_current_weather(session)
                weather_data = parse_current_weatherapi(weather_data_raw) if weather_data_raw else None
                description = f"текущая погода: {weather_data.get('description', '') if weather_data else ''}"
                caption_func = create_weather_caption_current

            elif message_type == "today":
                forecast_data_raw = await fetch_forecast_weather(session, days=1)
                weather_data = parse_today_weatherapi(forecast_data_raw) if forecast_data_raw else None
                description = "прогноз на сегодня"
                caption_func = create_weather_caption_today

            elif message_type == "3days":
                forecast_data_raw = await fetch_forecast_weather(session, days=3)
                weather_data = parse_forecast_weatherapi(forecast_data_raw) if forecast_data_raw else None
                description = "прогноз на 3 дня"
                caption_func = create_weather_caption_3days

            else:
                logger.error(f"Неизвестный тип сообщения: {message_type}")
                return

            if not weather_data:
                logger.error(f"Не удалось получить данные для {message_type}")
                return

            # Формируем новую подпись
            new_caption = caption_func(weather_data)
            new_caption = truncate_text(new_caption, 1024)

            # Получаем текущее время для Cerebras
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M (MSK)")

            # Выбираем новое изображение
            selected_image = await select_image(description, AVAILABLE_IMAGE_NAMES, current_time)

            # Проверяем, есть ли уже file_id для этого изображения
            file_id = storage.get_file_id(selected_image)

            if not file_id:
                # Если file_id нет, отправляем изображение "втихую" для получения file_id
                image_path = get_image_path(selected_image)
                try:
                    with open(image_path, 'rb') as photo:
                        temp_message = await context.bot.send_photo(
                            chat_id=channel_id,
                            photo=photo,
                            caption="Загрузка изображения..."
                        )

                    if temp_message.photo:
                        file_id = temp_message.photo[-1].file_id
                        storage.save_file_id(selected_image, file_id)

                    # Удаляем временное сообщение
                    await context.bot.delete_message(chat_id=channel_id, message_id=temp_message.message_id)

                except Exception as e:
                    logger.error(f"Ошибка загрузки нового изображения: {e}")
                    # Используем существующий file_id или пропускаем обновление изображения
                    file_id = None

            # Обновляем сообщение
            try:
                if file_id:
                    # Обновляем и изображение, и подпись
                    media = InputMediaPhoto(media=file_id, caption=new_caption, parse_mode='HTML')
                    await context.bot.edit_message_media(
                        chat_id=channel_id,
                        message_id=message_id,
                        media=media
                    )
                    logger.info(f"Обновлено изображение и подпись для {message_type}")
                else:
                    # Обновляем только подпись
                    await context.bot.edit_message_caption(
                        chat_id=channel_id,
                        message_id=message_id,
                        caption=new_caption,
                        parse_mode='HTML'
                    )
                    logger.info(f"Обновлена только подпись для {message_type}")

                # Обновляем временную метку в хранилище
                storage.update_last_update(message_type)

            except TelegramError as e:
                if "message is not modified" in str(e).lower():
                    logger.info(f"Сообщение {message_type} не изменилось, обновление не требуется")
                else:
                    logger.error(f"Ошибка обновления сообщения {message_type}: {e}")

    except Exception as e:
        logger.error(f"Ошибка в _update_weather_message для {message_type}: {e}")


# Вспомогательные функции

def get_storage() -> Storage:
    """
    Возвращает экземпляр хранилища.

    Returns:
        Storage: Объект хранилища
    """
    return storage


def has_active_messages() -> bool:
    """
    Проверяет, есть ли активные сообщения для обновления.

    Returns:
        bool: True если есть активные сообщения
    """
    return storage.has_active_messages()


async def clear_all_messages() -> bool:
    """
    Очищает все message_id (используется при перезапуске).

    Returns:
        bool: True если очистка прошла успешно
    """
    return storage.clear_all_messages()


def get_message_ids() -> Dict[str, Optional[int]]:
    """
    Получает все message_id.

    Returns:
        Dict[str, Optional[int]]: Словарь с message_id для каждого типа
    """
    return storage.get_all_message_ids()
