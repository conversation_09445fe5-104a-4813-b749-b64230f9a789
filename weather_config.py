"""
Конфигурационный модуль для Telegram-бота погоды.

Содержит константы и настройки: токен бота, API-ключи WeatherAPI.com и Cerebras,
координаты местоположения, часовой пояс, интервалы обновления,
список доступных изображений с путями и названиями, ID канала.

Все значения встроены в конфиг согласно этапу 3 плана разработки.
"""

from typing import Dict

# =============================================================================
# API ТОКЕНЫ И КЛЮЧИ
# =============================================================================

# Telegram Bot API токен (из плана разработки, этап 3)
TELEGRAM_TOKEN = '**********************************************'

# WeatherAPI.com API ключ (из плана разработки)
WEATHERAPI_KEY = 'ecdd38215c144d2588b142720253107'

# Cerebras API ключ (из плана разработки)
CEREBRAS_API_KEY = 'csk-cnjd9w9nx5kjtpj99936fexmhpxdc586ny3k6rc96rfn4rkw'

# =============================================================================
# ГЕОГРАФИЧЕСКИЕ КООРДИНАТЫ
# =============================================================================

# Координаты Гагаринского района Севастополя
LAT = 44.600  # Широта
LON = 33.517  # Долгота

# =============================================================================
# ВРЕМЕННЫЕ НАСТРОЙКИ
# =============================================================================

# Часовой пояс (Московское время)
TZ = 'Europe/Moscow'

# Интервалы обновления (в секундах)
UPDATE_INTERVAL_WEEK = 43200      # 12 часов для прогноза на 3 дня
UPDATE_INTERVAL_3DAYS = 43200     # 12 часов (альтернативное название)
UPDATE_INTERVAL_TODAY = 3600      # 1 час для прогноза на сегодня
UPDATE_INTERVAL_CURRENT = 600     # 10 минут для текущей погоды

# =============================================================================
# WEATHERAPI.COM НАСТРОЙКИ
# =============================================================================

# Базовые URL для WeatherAPI.com
WEATHERAPI_BASE_URL = 'http://api.weatherapi.com/v1'
WEATHERAPI_CURRENT_URL = f'{WEATHERAPI_BASE_URL}/current.json'
WEATHERAPI_FORECAST_URL = f'{WEATHERAPI_BASE_URL}/forecast.json'
WEATHERAPI_ASTRONOMY_URL = f'{WEATHERAPI_BASE_URL}/astronomy.json'

# =============================================================================
# CEREBRAS API НАСТРОЙКИ
# =============================================================================

# Cerebras API endpoint
CEREBRAS_API_URL = 'https://api.cerebras.ai/v1/chat/completions'

# Модель для использования
CEREBRAS_MODEL = 'qwen-3-235b-a22b-instruct-2507'

# =============================================================================
# TELEGRAM НАСТРОЙКИ
# =============================================================================

# ID канала (из плана разработки, этап 3)
CHANNEL_ID = '2769056078'

# =============================================================================
# ИЗОБРАЖЕНИЯ ПОГОДЫ
# =============================================================================

# Словарь доступных изображений для разных погодных условий
# Ключи - понятные названия для модели Qwen
# Значения - пути к файлам изображений
AVAILABLE_IMAGES: Dict[str, str] = {
    # Ясная погода
    'sunny_day': 'images/clear_day.png',
    'sunny_evening': 'images/clear_evening.png',
    'clear_night': 'images/clear_night.png',

    # Малооблачно
    'partly_cloudy_day': 'images/few_clouds_day.png',
    'partly_cloudy_evening': 'images/few_clouds_evening.png',
    'partly_cloudy_night': 'images/few_clouds_night.png',

    # Облачно
    'cloudy_day': 'images/cloudy_day.png',
    'cloudy_evening': 'images/cloudy_evening.png',
    'cloudy_night': 'images/cloudy_night.png',

    # Пасмурно
    'overcast_day': 'images/overcast_day.png',
    'overcast_evening': 'images/overcast_evening.png',
    'overcast_night': 'images/overcast_night.png',

    # Дождь
    'rainy_day': 'images/rain_day.png',
    'rainy_evening': 'images/rain_evening.png',
    'rainy_night': 'images/rain_night.png',

    # Сильный дождь
    'heavy_rain_day': 'images/heavy_rain_day.png',
    'heavy_rain_evening': 'images/heavy_rain_evening.png',
    'heavy_rain_night': 'images/heavy_rain_night.png',

    # Снег
    'snowy_day': 'images/snow_day.png',
    'snowy_evening': 'images/snow_evening.png',
    'snowy_night': 'images/snow_night.png',

    # Гроза
    'thunderstorm_day': 'images/thunderstorm_day.png',
    'thunderstorm_evening': 'images/thunderstorm_evening.png',
    'thunderstorm_night': 'images/thunderstorm_night.png',

    # Туман
    'foggy_day': 'images/fog_day.png',
    'foggy_evening': 'images/fog_evening.png',
    'foggy_night': 'images/fog_night.png',

    # Дымка
    'hazy_day': 'images/haze_day.png',
    'hazy_evening': 'images/haze_evening.png',
    'hazy_night': 'images/haze_night.png',

    # Ветрено
    'windy_day': 'images/windy_day.png',
    'windy_evening': 'images/windy_evening.png',
    'windy_night': 'images/windy_night.png',

    # Метель
    'blizzard_day': 'images/blizzard_day.png',
    'blizzard_evening': 'images/blizzard_evening.png',
    'blizzard_night': 'images/blizzard_night.png',

    # Ледяной дождь
    'freezing_rain_day': 'images/freezing_rain_day.png',
    'freezing_rain_evening': 'images/freezing_rain_evening.png',
    'freezing_rain_night': 'images/freezing_rain_night.png',

    # Град
    'hail_day': 'images/hail_day.png',
    'hail_evening': 'images/hail_evening.png',
    'hail_night': 'images/hail_night.png',

    # Мороз
    'frosty_day': 'images/frost_day.png',
    'frosty_evening': 'images/frost_evening.png',
    'frosty_night': 'images/frost_night.png',
}

# Список названий изображений для передачи в Cerebras API
AVAILABLE_IMAGE_NAMES = list(AVAILABLE_IMAGES.keys())

# =============================================================================
# НАСТРОЙКИ СООБЩЕНИЙ
# =============================================================================

# Ключевое слово для активации бота
TRIGGER_KEYWORD = 'weather'

# Максимальная длина подписи к изображению в Telegram
MAX_CAPTION_LENGTH = 1024

# =============================================================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# =============================================================================

# Уровень логирования
LOG_LEVEL = 'INFO'

# Формат логов
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# =============================================================================
# ФУНКЦИИ ПРОВЕРКИ КОНФИГУРАЦИИ
# =============================================================================

def validate_config() -> bool:
    """
    Проверяет корректность конфигурации.

    Returns:
        bool: True если конфигурация корректна, False иначе
    """
    import os

    # Все обязательные переменные теперь встроены в конфиг (этап 3)
    # Проверяем только их наличие
    if not TELEGRAM_TOKEN:
        print("ОШИБКА: Не указан TELEGRAM_TOKEN")
        return False

    if not CHANNEL_ID:
        print("ОШИБКА: Не указан CHANNEL_ID")
        return False

    # Проверка существования файлов изображений
    missing_images = []
    for name, path in AVAILABLE_IMAGES.items():
        if not os.path.exists(path):
            missing_images.append(f"{name}: {path}")

    if missing_images:
        print("ОШИБКА: Отсутствуют файлы изображений:")
        for img in missing_images:
            print(f"  - {img}")
        return False

    print("✅ Конфигурация корректна")
    print(f"📍 Координаты: {LAT}, {LON}")
    print(f"🕐 Часовой пояс: {TZ}")
    print(f"🖼️ Доступно изображений: {len(AVAILABLE_IMAGES)}")
    return True


def get_image_path(image_name: str) -> str:
    """
    Получает путь к файлу изображения по его названию.

    Args:
        image_name: Название изображения

    Returns:
        str: Путь к файлу или путь к изображению по умолчанию
    """
    return AVAILABLE_IMAGES.get(image_name, AVAILABLE_IMAGES.get('cloudy_day', 'images/cloudy_day.png'))


if __name__ == "__main__":
    # Проверка конфигурации при запуске модуля
    validate_config()
