2025-07-31 18:37:54,603 - weather_storage - INFO - Данные загружены из weather_state.json
2025-07-31 18:37:54,604 - weather_storage - ERROR - Неизвестный тип сообщения: test
2025-07-31 18:37:54,604 - weather_storage - ERROR - Неизвестный тип сообщения: test
2025-07-31 18:38:11,696 - weather_storage - INFO - Данные загружены из weather_state.json
2025-07-31 18:38:11,696 - weather_storage - INFO - Обновлен message_id для current: 12345
2025-07-31 18:38:11,697 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:05:22,659 - __main__ - INFO - 🚀 Запуск Telegram-бота погоды...
2025-07-31 20:05:22,659 - __main__ - INFO - 📋 Проверка конфигурации...
2025-07-31 20:05:22,661 - __main__ - INFO - ✅ Конфигурация валидна
2025-07-31 20:05:22,661 - __main__ - INFO - 💾 Инициализация хранилища...
2025-07-31 20:05:22,661 - weather_storage - INFO - Данные загружены из weather_state.json
2025-07-31 20:05:22,661 - __main__ - INFO - ✅ Хранилище инициализировано
2025-07-31 20:05:22,661 - __main__ - INFO - 🤖 Создание Application...
2025-07-31 20:05:23,245 - __main__ - INFO - ✅ Application создан
2025-07-31 20:05:23,245 - __main__ - INFO - 📡 Регистрация обработчиков...
2025-07-31 20:05:23,245 - __main__ - INFO - ✅ Обработчики зарегистрированы
2025-07-31 20:05:23,245 - __main__ - INFO - ⏰ Проверка сохраненных сообщений для планирования обновлений...
2025-07-31 20:05:23,246 - __main__ - INFO - 📨 Найдено сохраненное сообщение типа 'current': 12345
2025-07-31 20:05:23,246 - __main__ - INFO - 🔄 Планирование задач обновления...
2025-07-31 20:05:23,246 - weather_scheduler - WARNING - Отсутствуют message_id для типов: ['3days', 'today', 'current']
2025-07-31 20:05:23,246 - weather_scheduler - WARNING - Планировщик запущен частично
2025-07-31 20:05:23,246 - weather_scheduler - ERROR - ❌ Не удалось запланировать ни одной задачи
2025-07-31 20:05:23,246 - __main__ - WARNING - ⚠️ Не удалось запланировать некоторые задачи обновления
2025-07-31 20:05:23,246 - __main__ - INFO - 🎯 Запуск polling...
2025-07-31 20:05:23,246 - __main__ - INFO - 📢 Бот будет отслеживать канал: 2769056078
2025-07-31 20:05:23,247 - __main__ - INFO - 🔍 Ожидание сообщений с ключевым словом 'weather'...
2025-07-31 20:05:23,247 - __main__ - ERROR - 💥 Критическая ошибка: Cannot close a running event loop
2025-07-31 20:05:23,247 - __main__ - INFO - 🛑 Завершение работы бота
2025-07-31 20:05:23,248 - __main__ - ERROR - 💥 Фатальная ошибка: Cannot close a running event loop
2025-07-31 20:06:06,454 - __main__ - INFO - 🚀 Запуск Telegram-бота погоды...
2025-07-31 20:06:06,454 - __main__ - INFO - 📋 Проверка конфигурации...
2025-07-31 20:06:06,455 - __main__ - INFO - ✅ Конфигурация валидна
2025-07-31 20:06:06,455 - __main__ - INFO - 💾 Инициализация хранилища...
2025-07-31 20:06:06,455 - weather_storage - INFO - Данные загружены из weather_state.json
2025-07-31 20:06:06,456 - __main__ - INFO - ✅ Хранилище инициализировано
2025-07-31 20:06:06,456 - __main__ - INFO - 🤖 Создание Application...
2025-07-31 20:06:06,930 - __main__ - INFO - ✅ Application создан
2025-07-31 20:06:06,930 - __main__ - INFO - 📡 Регистрация обработчиков...
2025-07-31 20:06:06,930 - __main__ - INFO - ✅ Обработчики зарегистрированы
2025-07-31 20:06:06,930 - __main__ - INFO - ⏰ Проверка сохраненных сообщений для планирования обновлений...
2025-07-31 20:06:06,930 - __main__ - INFO - 📨 Найдено сохраненное сообщение типа 'current': 12345
2025-07-31 20:06:06,930 - __main__ - INFO - 🔄 Планирование задач обновления...
2025-07-31 20:06:06,930 - weather_scheduler - WARNING - Отсутствуют message_id для типов: ['3days', 'today', 'current']
2025-07-31 20:06:06,931 - weather_scheduler - WARNING - Планировщик запущен частично
2025-07-31 20:06:06,931 - weather_scheduler - ERROR - ❌ Не удалось запланировать ни одной задачи
2025-07-31 20:06:06,931 - __main__ - WARNING - ⚠️ Не удалось запланировать некоторые задачи обновления
2025-07-31 20:06:06,931 - __main__ - INFO - 🎯 Запуск polling...
2025-07-31 20:06:06,931 - __main__ - INFO - 📢 Бот будет отслеживать канал: 2769056078
2025-07-31 20:06:06,931 - __main__ - INFO - 🔍 Ожидание сообщений с ключевым словом 'weather'...
2025-07-31 20:06:07,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getMe "HTTP/1.1 200 OK"
2025-07-31 20:06:07,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/deleteWebhook "HTTP/1.1 200 OK"
2025-07-31 20:06:07,440 - apscheduler.scheduler - INFO - Scheduler started
2025-07-31 20:06:07,440 - telegram.ext.Application - INFO - Application started
2025-07-31 20:06:17,743 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:06:27,884 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:06:37,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:06:48,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:06:58,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:08,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:18,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:28,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:38,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:48,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:51,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:07:51,680 - weather_storage - INFO - Установлен channel_id: -1002769056078
2025-07-31 20:07:51,681 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:51,681 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:51,681 - weather_handlers - INFO - Обнаружено ключевое слово 'weather' в канале -1002769056078
2025-07-31 20:07:51,682 - weather_handlers - INFO - Начинаем отправку начальных сообщений в канал -1002769056078
2025-07-31 20:07:51,682 - weather_api - INFO - Запрос текущей погоды для координат 44.6, 33.517
2025-07-31 20:07:52,283 - weather_api - INFO - Текущая погода успешно получена
2025-07-31 20:07:52,283 - weather_api - INFO - Запрос прогноза погоды на 3 дней для координат 44.6, 33.517
2025-07-31 20:07:52,393 - weather_api - INFO - Прогноз погоды на 3 дней успешно получен
2025-07-31 20:07:52,393 - weather_api - INFO - Текущая погода: 24°C, Солнечно
2025-07-31 20:07:52,395 - weather_api - INFO - Прогноз на 3 дней успешно обработан
2025-07-31 20:07:52,398 - weather_api - INFO - Данные на сегодня успешно обработаны
2025-07-31 20:07:53,061 - weather_cerebras - INFO - Cerebras выбрал изображение: sunny_evening
2025-07-31 20:07:53,061 - weather_handlers - INFO - Отправляем 3days: изображение sunny_evening
2025-07-31 20:07:54,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/sendPhoto "HTTP/1.1 200 OK"
2025-07-31 20:07:54,272 - weather_storage - INFO - Обновлен message_id для 3days: 3
2025-07-31 20:07:54,272 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:54,273 - weather_storage - INFO - Сохранен file_id для sunny_evening: AgACAgIAAyEGAASlDHFOAAMDaIui6gZhzlImMbKbAAGqIXXyybOHAAIO-TEbzv5hSGvAQJoEi1lfAQADAgADdwADNgQ
2025-07-31 20:07:54,273 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:54,273 - weather_handlers - INFO - Сообщение 3days отправлено, message_id: 3
2025-07-31 20:07:55,073 - weather_cerebras - INFO - Cerebras выбрал изображение: sunny_evening
2025-07-31 20:07:55,074 - weather_handlers - INFO - Отправляем today: изображение sunny_evening
2025-07-31 20:07:55,658 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/sendPhoto "HTTP/1.1 200 OK"
2025-07-31 20:07:55,659 - weather_storage - INFO - Обновлен message_id для today: 4
2025-07-31 20:07:55,659 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:55,659 - weather_storage - INFO - Сохранен file_id для sunny_evening: AgACAgIAAyEGAASlDHFOAAMDaIui6gZhzlImMbKbAAGqIXXyybOHAAIO-TEbzv5hSGvAQJoEi1lfAQADAgADdwADNgQ
2025-07-31 20:07:55,660 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:55,660 - weather_handlers - INFO - Сообщение today отправлено, message_id: 4
2025-07-31 20:07:56,201 - weather_cerebras - INFO - Cerebras выбрал изображение: sunny_evening
2025-07-31 20:07:56,201 - weather_handlers - INFO - Отправляем current: изображение sunny_evening
2025-07-31 20:07:56,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/sendPhoto "HTTP/1.1 200 OK"
2025-07-31 20:07:56,731 - weather_storage - INFO - Обновлен message_id для current: 5
2025-07-31 20:07:56,731 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:56,732 - weather_storage - INFO - Сохранен file_id для sunny_evening: AgACAgIAAyEGAASlDHFOAAMDaIui6gZhzlImMbKbAAGqIXXyybOHAAIO-TEbzv5hSGvAQJoEi1lfAQADAgADdwADNgQ
2025-07-31 20:07:56,732 - weather_storage - INFO - Данные сохранены в weather_state.json
2025-07-31 20:07:56,732 - weather_handlers - INFO - Сообщение current отправлено, message_id: 5
2025-07-31 20:07:56,732 - weather_handlers - INFO - Все начальные сообщения отправлены успешно
2025-07-31 20:08:01,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:08:11,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:08:21,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 20:08:32,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
