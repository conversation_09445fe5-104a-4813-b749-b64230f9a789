#!/usr/bin/env python3
"""
Тест для модуля weather_api.py

Проверяет работу функций для получения и парсинга данных от WeatherAPI.com
"""

import asyncio
import aiohttp
import logging
from weather_api import (
    fetch_current_weather,
    fetch_forecast_weather,
    fetch_astronomy_data,
    parse_current_weatherapi,
    parse_forecast_weatherapi,
    parse_today_weatherapi,
    get_cache_status,
    clear_cache
)

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_weather_api():
    """Тестирует все функции weather_api модуля"""
    
    print("🧪 Начинаем тестирование weather_api модуля...")
    print("=" * 60)
    
    # Очищаем кэш перед тестами
    clear_cache()
    print("🗑️ Кэш очищен")
    
    async with aiohttp.ClientSession() as session:
        
        # Тест 1: Получение текущей погоды
        print("\n1️⃣ Тестируем получение текущей погоды...")
        current_data = await fetch_current_weather(session)
        
        if current_data:
            print("✅ Текущая погода получена успешно")
            
            # Парсим данные
            parsed_current = parse_current_weatherapi(current_data)
            if parsed_current:
                print(f"   Температура: {parsed_current['temp']}°C")
                print(f"   Ощущается как: {parsed_current['feels_like']}°C")
                print(f"   Описание: {parsed_current['description']}")
                print(f"   Ветер: {parsed_current['wind_speed']} м/с {parsed_current['wind_dir']}")
                print("✅ Парсинг текущей погоды успешен")
            else:
                print("❌ Ошибка парсинга текущей погоды")
        else:
            print("❌ Не удалось получить текущую погоду")
        
        # Тест 2: Получение прогноза на 3 дня
        print("\n2️⃣ Тестируем получение прогноза на 3 дня...")
        forecast_data = await fetch_forecast_weather(session, days=3)
        
        if forecast_data:
            print("✅ Прогноз на 3 дня получен успешно")
            
            # Парсим данные
            parsed_forecast = parse_forecast_weatherapi(forecast_data)
            if parsed_forecast:
                print(f"   Получено дней: {len(parsed_forecast)}")
                for i, day in enumerate(parsed_forecast):
                    print(f"   День {i+1}: {day['date']}, {day['temp_min']}-{day['temp_max']}°C, {day['description']}")
                print("✅ Парсинг прогноза на 3 дня успешен")
            else:
                print("❌ Ошибка парсинга прогноза на 3 дня")
        else:
            print("❌ Не удалось получить прогноз на 3 дня")
        
        # Тест 3: Получение данных на сегодня
        print("\n3️⃣ Тестируем получение данных на сегодня...")
        if forecast_data:
            parsed_today = parse_today_weatherapi(forecast_data)
            if parsed_today:
                print("✅ Данные на сегодня получены успешно")
                print(f"   Восход: {parsed_today.get('sunrise_display', 'Неизвестно')}")
                print(f"   Закат: {parsed_today['sunset_formatted']}")
                
                for period_key, period_data in parsed_today['periods'].items():
                    print(f"   {period_data['name']}: {period_data['temp']}°C, {period_data['description']}")
                print("✅ Парсинг данных на сегодня успешен")
            else:
                print("❌ Ошибка парсинга данных на сегодня")
        
        # Тест 4: Получение астрономических данных
        print("\n4️⃣ Тестируем получение астрономических данных...")
        astronomy_data = await fetch_astronomy_data(session)
        
        if astronomy_data:
            print("✅ Астрономические данные получены успешно")
            astro = astronomy_data.get('astronomy', {})
            print(f"   Восход: {astro.get('sunrise', 'Неизвестно')}")
            print(f"   Закат: {astro.get('sunset', 'Неизвестно')}")
            print(f"   Фаза луны: {astro.get('moon_phase', 'Неизвестно')}")
        else:
            print("❌ Не удалось получить астрономические данные")
    
    # Тест 5: Проверка кэша
    print("\n5️⃣ Тестируем кэширование...")
    cache_status = get_cache_status()
    print("📊 Статус кэша:")
    for key, status in cache_status.items():
        print(f"   {key}: {'✅ валиден' if status['is_valid'] else '❌ невалиден'} "
              f"(возраст: {status['age_seconds']}с)")
    
    print("\n" + "=" * 60)
    print("🎉 Тестирование завершено!")


async def test_cache_functionality():
    """Тестирует функциональность кэширования"""
    
    print("\n🔄 Тестируем кэширование...")
    
    async with aiohttp.ClientSession() as session:
        # Первый запрос - должен обратиться к API
        print("   Первый запрос (должен обратиться к API)...")
        start_time = asyncio.get_event_loop().time()
        data1 = await fetch_current_weather(session)
        time1 = asyncio.get_event_loop().time() - start_time
        
        # Второй запрос - должен использовать кэш
        print("   Второй запрос (должен использовать кэш)...")
        start_time = asyncio.get_event_loop().time()
        data2 = await fetch_current_weather(session)
        time2 = asyncio.get_event_loop().time() - start_time
        
        print(f"   Время первого запроса: {time1:.3f}с")
        print(f"   Время второго запроса: {time2:.3f}с")
        
        if time2 < time1 * 0.1:  # Кэшированный запрос должен быть намного быстрее
            print("✅ Кэширование работает корректно")
        else:
            print("⚠️ Кэширование может работать некорректно")


if __name__ == "__main__":
    print("🌤️ Тест модуля weather_api.py")
    print("Проверяем работу с WeatherAPI.com")
    print()
    
    try:
        # Основные тесты
        asyncio.run(test_weather_api())
        
        # Тест кэширования
        asyncio.run(test_cache_functionality())
        
    except KeyboardInterrupt:
        print("\n⏹️ Тестирование прервано пользователем")
    except Exception as e:
        print(f"\n❌ Ошибка во время тестирования: {e}")
        logger.exception("Подробности ошибки:")
