"""
Тесты для модуля weather_scheduler.py

Проверяет корректность работы планировщика задач.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import logging

# Настройка логирования для тестов
logging.basicConfig(level=logging.INFO)

# Импорт тестируемого модуля
from weather_scheduler import schedule_updates, stop_all_jobs, get_job_status, restart_scheduler


class TestWeatherScheduler(unittest.TestCase):
    """Тесты для планировщика задач погодного бота."""
    
    def setUp(self):
        """Подготовка к тестам."""
        # Создаем мок объекты
        self.mock_application = Mock()
        self.mock_job_queue = Mock()
        self.mock_storage = Mock()
        
        # Настраиваем мок application
        self.mock_application.job_queue = self.mock_job_queue
        
        # Настраиваем мок storage с тестовыми данными
        self.test_data = {
            '3days': {'message_id': 123},
            'today': {'message_id': 456}, 
            'current': {'message_id': 789}
        }
        self.mock_storage.load.return_value = self.test_data
    
    def test_schedule_updates_success(self):
        """Тест успешного планирования задач."""
        # Выполняем функцию
        result = schedule_updates(self.mock_application, self.mock_storage)
        
        # Проверяем результат
        self.assertTrue(result)
        
        # Проверяем, что storage.load() был вызван
        self.mock_storage.load.assert_called_once()
        
        # Проверяем, что run_repeating был вызван 3 раза (для каждого типа сообщения)
        self.assertEqual(self.mock_job_queue.run_repeating.call_count, 3)
        
        # Проверяем параметры первого вызова (3days)
        first_call = self.mock_job_queue.run_repeating.call_args_list[0]
        self.assertEqual(first_call[1]['name'], 'update_3days')
        self.assertEqual(first_call[1]['first'], 0)
        self.assertEqual(first_call[1]['data']['type'], '3days')
        self.assertEqual(first_call[1]['data']['message_id'], 123)
    
    def test_schedule_updates_no_job_queue(self):
        """Тест когда JobQueue недоступен."""
        # Убираем job_queue
        self.mock_application.job_queue = None
        
        # Выполняем функцию
        result = schedule_updates(self.mock_application, self.mock_storage)
        
        # Проверяем результат
        self.assertFalse(result)
    
    def test_schedule_updates_empty_storage(self):
        """Тест когда хранилище пустое."""
        # Настраиваем пустое хранилище
        self.mock_storage.load.return_value = {}
        
        # Выполняем функцию
        result = schedule_updates(self.mock_application, self.mock_storage)
        
        # Проверяем результат
        self.assertFalse(result)
    
    def test_schedule_updates_partial_data(self):
        """Тест когда в хранилище есть только часть данных."""
        # Настраиваем частичные данные
        partial_data = {
            '3days': {'message_id': 123},
            'today': {'message_id': 456}
            # current отсутствует
        }
        self.mock_storage.load.return_value = partial_data
        
        # Выполняем функцию
        result = schedule_updates(self.mock_application, self.mock_storage)
        
        # Проверяем результат (должен быть True, так как есть хотя бы одна задача)
        self.assertTrue(result)
        
        # Проверяем, что run_repeating был вызван 2 раза
        self.assertEqual(self.mock_job_queue.run_repeating.call_count, 2)
    
    def test_stop_all_jobs_success(self):
        """Тест успешной остановки задач."""
        # Создаем мок задачи
        mock_job1 = Mock()
        mock_job1.name = 'update_3days'
        mock_job2 = Mock()
        mock_job2.name = 'update_today'
        mock_job3 = Mock()
        mock_job3.name = 'other_job'
        
        # Настраиваем job_queue.jobs()
        self.mock_job_queue.jobs.return_value = [mock_job1, mock_job2, mock_job3]
        
        # Выполняем функцию
        result = stop_all_jobs(self.mock_application)
        
        # Проверяем результат
        self.assertTrue(result)
        
        # Проверяем, что schedule_removal был вызван только для weather задач (update_*)
        mock_job1.schedule_removal.assert_called_once()
        mock_job2.schedule_removal.assert_called_once()
        # mock_job3 не должен быть остановлен, так как его имя не начинается с 'update_'
        mock_job3.schedule_removal.assert_not_called()
    
    def test_stop_all_jobs_no_job_queue(self):
        """Тест остановки задач когда JobQueue недоступен."""
        # Убираем job_queue
        self.mock_application.job_queue = None
        
        # Выполняем функцию
        result = stop_all_jobs(self.mock_application)
        
        # Проверяем результат
        self.assertFalse(result)
    
    def test_get_job_status_success(self):
        """Тест получения статуса задач."""
        # Создаем мок задачи
        mock_job = Mock()
        mock_job.name = 'update_3days'
        mock_job.enabled = True
        mock_job.interval = 43200
        
        # Настраиваем job_queue.jobs()
        self.mock_job_queue.jobs.return_value = [mock_job]
        
        # Выполняем функцию
        result = get_job_status(self.mock_application)
        
        # Проверяем результат
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_jobs'], 1)
        self.assertEqual(len(result['weather_jobs']), 1)
        self.assertEqual(result['weather_jobs'][0]['name'], 'update_3days')
    
    def test_restart_scheduler_success(self):
        """Тест перезапуска планировщика."""
        # Настраиваем мок задачи для остановки
        mock_job = Mock()
        mock_job.name = 'update_3days'
        self.mock_job_queue.jobs.return_value = [mock_job]
        
        # Выполняем функцию
        result = restart_scheduler(self.mock_application, self.mock_storage)
        
        # Проверяем результат
        self.assertTrue(result)
        
        # Проверяем, что задачи были остановлены и запущены заново
        mock_job.schedule_removal.assert_called_once()
        self.mock_storage.load.assert_called_once()


if __name__ == '__main__':
    print("🧪 Запуск тестов планировщика...")
    unittest.main(verbosity=2)
